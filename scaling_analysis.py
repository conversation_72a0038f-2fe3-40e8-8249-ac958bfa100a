"""Entry point for extracting data from neptune on scaling experiments."""

import argparse
import os
import pandas as pd
import neptune
from dotenv import load_dotenv
from typing import Iterable


def extract_data(run_id: str) -> pd.DataFrame:
    """Extract data from a single run"""
    load_dotenv(dotenv_path=os.path.expanduser("~/.neptune/.env"))
    neptune_api_token = os.environ["NEPTUNE_API_TOKEN"]
    neptune_run = neptune.init_run(
        with_id=run_id,
        project="markusrabeworkspace/training-exploration",
        api_token=neptune_api_token,
    )
    # out = neptune_run.fetch()
    # print(out)
    # return out
    out = neptune_run["train/loss"].download()
    print(out)
    return out.to_pandas()


def main(output_file: str, run_ids: Iterable[str]):
    """Main function"""
    df = pd.concat([extract_data(run_id) for run_id in run_ids])
    df.to_csv(output_file, index=False)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--run_ids", type=str, nargs="+")
    parser.add_argument("--output_file", type=str)
    args = parser.parse_args()
    main(output_file=args.output_file, run_ids=args.run_ids)
